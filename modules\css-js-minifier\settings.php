<?php
/**
 * CSS/JS Minifier Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$minifier = new Redco_CSS_JS_Minifier();
$is_enabled = redco_is_module_enabled('css-js-minifier');

// Get current settings
$minify_css = redco_get_module_option('css-js-minifier', 'minify_css', true);
$minify_js = redco_get_module_option('css-js-minifier', 'minify_js', true);
$minify_inline = redco_get_module_option('css-js-minifier', 'minify_inline', true);
$exclude_css = redco_get_module_option('css-js-minifier', 'exclude_css', array());
$exclude_js = redco_get_module_option('css-js-minifier', 'exclude_js', array());

// Get enqueued styles and scripts
$enqueued_styles = redco_get_enqueued_styles();
$enqueued_scripts = redco_get_enqueued_scripts();

// Get statistics - only if module is enabled
$stats = array('files_minified' => 0, 'bytes_saved' => 0);
if ($is_enabled && class_exists('Redco_CSS_JS_Minifier')) {
    $stats = $minifier->get_stats();
}
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-module-tab" data-module="css-js-minifier">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('CSS/JS Minifier', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-media-code"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('CSS/JS Minifier', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Optimize CSS and JavaScript files by removing unnecessary whitespace and comments for faster loading', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if ($minify_css): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-admin-appearance"></span>
                                    <?php _e('CSS Minify', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($minify_js): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-media-code"></span>
                                    <?php _e('JS Minify', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="enable-all-minification">
                                <span class="dashicons dashicons-yes"></span>
                                <?php _e('Enable All', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="clear-minified-cache" data-redco-action="clear_minified_cache">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Clear Cache', 'redco-optimizer'); ?>
                            </button>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="header-action-btn">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Diagnose', 'redco-optimizer'); ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo number_format($stats['files_minified']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Files', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $minify_css ? '✓' : '✗'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('CSS', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $minify_js ? '✓' : '✗'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('JS', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="css-js-minifier">
                    <!-- Minification Settings Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-performance"></span>
                                <?php _e('Minification Options', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Minification removes unnecessary characters from CSS and JavaScript files to reduce file sizes and improve loading speed. Enable the options that work best for your website.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Estimated Performance Gain:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level medium" id="minification-impact"><?php _e('Medium-High', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">

                                <div class="minification-options">
                                    <div class="minification-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[minify_css]" value="1" <?php checked($minify_css); ?> class="minification-checkbox">
                                            <span class="option-icon">🎨</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('CSS Minification', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Removes whitespace, comments, and unnecessary characters from CSS files. Typically reduces file size by 20-30%.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Size Reduction:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value">20-30%</span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value high"><?php _e('High', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="minification-option recommended">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[minify_js]" value="1" <?php checked($minify_js); ?> class="minification-checkbox">
                                            <span class="option-icon">⚡</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('JavaScript Minification', 'redco-optimizer'); ?></strong>
                                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Compresses JavaScript files by removing whitespace and shortening variable names. Can reduce file size by 30-50%.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Size Reduction:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value">30-50%</span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Medium', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="minification-option advanced">
                                        <label class="option-checkbox-item">
                                            <input type="checkbox" name="settings[minify_inline]" value="1" <?php checked($minify_inline); ?> class="minification-checkbox">
                                            <span class="option-icon">📝</span>
                                            <span class="option-content">
                                                <span class="option-title">
                                                    <strong><?php _e('Inline Code Minification', 'redco-optimizer'); ?></strong>
                                                    <span class="advanced-badge"><?php _e('Advanced', 'redco-optimizer'); ?></span>
                                                </span>
                                                <span class="option-description">
                                                    <?php _e('Minifies CSS and JavaScript code embedded directly in HTML pages. Provides additional optimization for inline code.', 'redco-optimizer'); ?>
                                                </span>
                                                <div class="option-stats">
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Size Reduction:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value">10-20%</span>
                                                    </span>
                                                    <span class="stat-item">
                                                        <span class="stat-label"><?php _e('Compatibility:', 'redco-optimizer'); ?></span>
                                                        <span class="stat-value medium"><?php _e('Medium', 'redco-optimizer'); ?></span>
                                                    </span>
                                                </div>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="minification-summary">
                                    <div class="summary-stats">
                                        <span class="enabled-count">0 <?php _e('options enabled', 'redco-optimizer'); ?></span>
                                        <span class="estimated-savings"><?php _e('Estimated savings: 0%', 'redco-optimizer'); ?></span>
                                    </div>
                                    <div class="compatibility-notice">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Test your website after enabling minification to ensure everything works correctly.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($enqueued_styles)): ?>
                    <!-- Exclude CSS Files Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-appearance"></span>
                                <?php _e('Exclude CSS Files', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Select CSS files that should not be minified (e.g., files that break when minified).', 'redco-optimizer'); ?>
                            </p>

                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('CSS Files to Exclude', 'redco-optimizer'); ?></th>
                                    <td>
                                        <div class="checkbox-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                            <?php foreach ($enqueued_styles as $handle => $style): ?>
                                                <div class="checkbox-item" style="margin-bottom: 8px;">
                                                    <label>
                                                        <input type="checkbox" name="settings[exclude_css][]"
                                                               value="<?php echo esc_attr($handle); ?>"
                                                               <?php checked(in_array($handle, $exclude_css)); ?>>
                                                        <strong><?php echo esc_html($handle); ?></strong>
                                                        <?php if (!empty($style['src'])): ?>
                                                            <br><small style="color: #666;"><?php echo esc_html(basename($style['src'])); ?></small>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <p class="description">
                                            <?php echo sprintf(__('%d CSS files found. Select files to exclude from minification.', 'redco-optimizer'), count($enqueued_styles)); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($enqueued_scripts)): ?>
                    <!-- Exclude JavaScript Files Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-media-code"></span>
                                <?php _e('Exclude JavaScript Files', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <p class="description">
                                <?php _e('Select JavaScript files that should not be minified (e.g., files that break when minified).', 'redco-optimizer'); ?>
                            </p>

                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('JavaScript Files to Exclude', 'redco-optimizer'); ?></th>
                                    <td>
                                        <div class="checkbox-list" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                            <?php foreach ($enqueued_scripts as $handle => $script): ?>
                                                <div class="checkbox-item" style="margin-bottom: 8px;">
                                                    <label>
                                                        <input type="checkbox" name="settings[exclude_js][]"
                                                               value="<?php echo esc_attr($handle); ?>"
                                                               <?php checked(in_array($handle, $exclude_js)); ?>>
                                                        <strong><?php echo esc_html($handle); ?></strong>
                                                        <?php if (!empty($script['src'])): ?>
                                                            <br><small style="color: #666;"><?php echo esc_html(basename($script['src'])); ?></small>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <p class="description">
                                            <?php echo sprintf(__('%d JavaScript files found. Select files to exclude from minification.', 'redco-optimizer'), count($enqueued_scripts)); ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Module Statistics', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-files">
                                <span class="stat-value"><?php echo number_format($stats['files_minified']); ?></span>
                                <span class="stat-label"><?php _e('Files Minified', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-saved">
                                <span class="stat-value"><?php echo redco_format_bytes($stats['bytes_saved']); ?></span>
                                <span class="stat-label"><?php _e('Bytes Saved', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-css">
                                <span class="stat-value"><?php echo $minify_css ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('CSS Minify', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-js">
                                <span class="stat-value"><?php echo $minify_js ? __('Yes', 'redco-optimizer') : __('No', 'redco-optimizer'); ?></span>
                                <span class="stat-label"><?php _e('JS Minify', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cache Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Cache Management', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <button type="button" id="clear-minified-cache" class="button button-secondary" data-redco-action="clear_minified_cache" style="width: 100%; margin-bottom: 10px;">
                            <span class="dashicons dashicons-trash"></span>
                            <?php _e('Clear Minified Cache', 'redco-optimizer'); ?>
                        </button>
                        <p class="description">
                            <?php _e('Clear all minified files. New minified versions will be generated when pages are visited.', 'redco-optimizer'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-media-code"></span>
            <h3><?php _e('CSS/JS Minifier Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to access CSS and JavaScript minification features.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>
