<?php
/**
 * Module Consolidation Notice Handler
 * 
 * Handles admin notices and migration prompts for module consolidation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Consolidation_Notice {

    /**
     * Initialize the notice system
     */
    public static function init() {
        add_action('admin_notices', array(__CLASS__, 'show_consolidation_notice'));
        add_action('wp_ajax_redco_migrate_to_asset_optimization', array(__CLASS__, 'ajax_migrate_modules'));
        add_action('wp_ajax_redco_dismiss_consolidation_notice', array(__CLASS__, 'ajax_dismiss_notice'));
    }

    /**
     * Show consolidation notice if migration is needed
     */
    public static function show_consolidation_notice() {
        // Only show on RedCo Optimizer admin pages
        if (!isset($_GET['page']) || strpos($_GET['page'], 'redco-optimizer') === false) {
            return;
        }

        // Check if notice was dismissed
        if (get_option('redco_consolidation_notice_dismissed', false)) {
            return;
        }

        // Check if migration is needed
        if (!self::needs_migration()) {
            return;
        }

        $css_js_enabled = redco_is_module_enabled('css-js-minifier');
        $critical_enabled = redco_is_module_enabled('critical-resource-optimizer');
        $asset_optimization_enabled = redco_is_module_enabled('asset-optimization');

        ?>
        <div class="notice notice-info redco-consolidation-notice" style="position: relative; padding: 15px;">
            <div style="display: flex; align-items: flex-start; gap: 15px;">
                <div style="flex-shrink: 0;">
                    <span class="dashicons dashicons-performance" style="color: #4CAF50; font-size: 24px; margin-top: 2px;"></span>
                </div>
                <div style="flex: 1;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">
                        <?php _e('🚀 Module Consolidation Available!', 'redco-optimizer'); ?>
                    </h3>
                    <p style="margin: 0 0 15px 0; font-size: 14px; line-height: 1.5;">
                        <?php _e('We\'ve created a new <strong>Asset Optimization</strong> module that combines and improves upon your existing modules:', 'redco-optimizer'); ?>
                    </p>
                    
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin: 15px 0;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <h4 style="margin: 0 0 8px 0; color: #666; font-size: 13px; text-transform: uppercase;">
                                    <?php _e('Current Modules', 'redco-optimizer'); ?>
                                </h4>
                                <ul style="margin: 0; padding-left: 20px; font-size: 13px;">
                                    <?php if ($css_js_enabled): ?>
                                        <li style="color: #d63384;">✓ CSS/JS Minifier</li>
                                    <?php endif; ?>
                                    <?php if ($critical_enabled): ?>
                                        <li style="color: #d63384;">✓ Critical Resource Optimizer</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <div>
                                <h4 style="margin: 0 0 8px 0; color: #666; font-size: 13px; text-transform: uppercase;">
                                    <?php _e('New Unified Module', 'redco-optimizer'); ?>
                                </h4>
                                <ul style="margin: 0; padding-left: 20px; font-size: 13px;">
                                    <li style="color: #4CAF50;">✨ Asset Optimization (All-in-One)</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; border-left: 4px solid #4CAF50; margin: 15px 0;">
                        <h4 style="margin: 0 0 8px 0; color: #2e7d32; font-size: 14px;">
                            <?php _e('✨ Benefits of Migration:', 'redco-optimizer'); ?>
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; font-size: 13px; color: #2e7d32;">
                            <li>Unified settings interface</li>
                            <li>Better performance optimization</li>
                            <li>Reduced memory usage</li>
                            <li>Enhanced caching system</li>
                            <li>Simplified management</li>
                        </ul>
                    </div>

                    <div style="display: flex; gap: 10px; align-items: center; margin-top: 15px;">
                        <button type="button" class="button button-primary" id="migrate-to-asset-optimization" style="background: #4CAF50; border-color: #4CAF50;">
                            <span class="dashicons dashicons-update" style="margin-right: 5px;"></span>
                            <?php _e('Migrate Now (Recommended)', 'redco-optimizer'); ?>
                        </button>
                        <button type="button" class="button button-secondary" id="dismiss-consolidation-notice">
                            <?php _e('Maybe Later', 'redco-optimizer'); ?>
                        </button>
                        <span class="migration-status" style="margin-left: 10px; font-style: italic; color: #666;"></span>
                    </div>

                    <p style="margin: 10px 0 0 0; font-size: 12px; color: #666;">
                        <?php _e('💡 Migration is safe and reversible. Your settings will be preserved and improved.', 'redco-optimizer'); ?>
                    </p>
                </div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // Migration button handler
            $('#migrate-to-asset-optimization').on('click', function() {
                const button = $(this);
                const status = $('.migration-status');
                
                button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Migrating...');
                status.text('Starting migration...');

                $.post(ajaxurl, {
                    action: 'redco_migrate_to_asset_optimization',
                    nonce: '<?php echo wp_create_nonce('redco_consolidation_nonce'); ?>'
                }, function(response) {
                    if (response.success) {
                        status.html('<span style="color: #4CAF50;">✓ Migration completed successfully!</span>');
                        
                        // Show success message
                        $('.redco-consolidation-notice').html(
                            '<div style="text-align: center; padding: 20px;">' +
                            '<span class="dashicons dashicons-yes-alt" style="color: #4CAF50; font-size: 32px;"></span>' +
                            '<h3 style="color: #4CAF50; margin: 10px 0;">Migration Completed Successfully!</h3>' +
                            '<p>Your modules have been consolidated into the new Asset Optimization module.</p>' +
                            '<button type="button" class="button button-primary" onclick="location.reload()">Refresh Page</button>' +
                            '</div>'
                        );
                        
                        // Auto-refresh after 3 seconds
                        setTimeout(() => location.reload(), 3000);
                    } else {
                        status.html('<span style="color: #d63384;">✗ Migration failed: ' + (response.data || 'Unknown error') + '</span>');
                        button.prop('disabled', false).html('<span class="dashicons dashicons-update"></span> Retry Migration');
                    }
                }).fail(function() {
                    status.html('<span style="color: #d63384;">✗ Migration failed: Network error</span>');
                    button.prop('disabled', false).html('<span class="dashicons dashicons-update"></span> Retry Migration');
                });
            });

            // Dismiss notice handler
            $('#dismiss-consolidation-notice').on('click', function() {
                $.post(ajaxurl, {
                    action: 'redco_dismiss_consolidation_notice',
                    nonce: '<?php echo wp_create_nonce('redco_consolidation_nonce'); ?>'
                }, function() {
                    $('.redco-consolidation-notice').fadeOut();
                });
            });
        });

        .spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        </script>

        <style>
        .redco-consolidation-notice {
            border-left: 4px solid #4CAF50 !important;
        }
        .redco-consolidation-notice .dashicons.spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        </style>
        <?php
    }

    /**
     * Check if migration is needed
     */
    private static function needs_migration() {
        // Check if Asset Optimization migration is complete
        if (get_option('redco_asset_optimization_migration_complete', false)) {
            return false;
        }

        // Check if old modules are enabled
        $css_js_enabled = redco_is_module_enabled('css-js-minifier');
        $critical_enabled = redco_is_module_enabled('critical-resource-optimizer');

        return $css_js_enabled || $critical_enabled;
    }

    /**
     * AJAX handler for module migration
     */
    public static function ajax_migrate_modules() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_consolidation_nonce')) {
            wp_die('Security check failed');
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Load migration class
        require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/asset-optimization/migration.php';

        // Run migration
        $result = Redco_Asset_Optimization_Migration::migrate();

        if ($result['success']) {
            // Dismiss the notice
            update_option('redco_consolidation_notice_dismissed', true);
            
            wp_send_json_success(array(
                'message' => $result['message'],
                'log' => $result['log']
            ));
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for dismissing notice
     */
    public static function ajax_dismiss_notice() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_consolidation_nonce')) {
            wp_die('Security check failed');
        }

        update_option('redco_consolidation_notice_dismissed', true);
        wp_send_json_success();
    }
}

// Initialize the consolidation notice system
Redco_Consolidation_Notice::init();
