<?php
// Debug attachment URL generation
require_once '../../../wp-config.php';

global $wpdb;

echo "=== ATTACHMENT URL DEBUG ===\n\n";

// Get the converted image (2021-10-10)
$latest_result = $wpdb->get_results("
    SELECT p.ID, p.post_title, p.guid
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
    WHERE p.post_type = 'attachment'
    AND p.post_mime_type LIKE 'image/%'
    AND pm.meta_key = '_webp_conversion_data'
    ORDER BY p.ID DESC
    LIMIT 1
");

if (empty($latest_result)) {
    echo "No images found.\n";
    exit;
}

$attachment_id = $latest_result[0]->ID;
echo "Testing with attachment ID: {$attachment_id}\n";
echo "Post title: {$latest_result[0]->post_title}\n";
echo "GUID: {$latest_result[0]->guid}\n\n";

// Test different URL functions
echo "=== URL FUNCTION TESTS ===\n";

// Test wp_get_attachment_url
$attachment_url = wp_get_attachment_url($attachment_id);
echo "wp_get_attachment_url(): {$attachment_url}\n";

// Test get_attached_file
$attached_file = get_attached_file($attachment_id);
echo "get_attached_file(): {$attached_file}\n";

// Convert file path to URL manually
$upload_dir = wp_upload_dir();
$manual_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $attached_file);
$manual_url = wp_normalize_path($manual_url);
$manual_url = str_replace('\\', '/', $manual_url);
echo "Manual path-to-URL conversion: {$manual_url}\n";

// Test attachment metadata
$metadata = wp_get_attachment_metadata($attachment_id);
echo "Attachment metadata file: " . ($metadata['file'] ?? 'NONE') . "\n";

// Check conversion data
$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
echo "\n=== CONVERSION DATA ===\n";
if (is_array($conversion_data)) {
    echo "Converted: " . ($conversion_data['converted'] ? 'YES' : 'NO') . "\n";
    if (isset($conversion_data['webp_url'])) {
        echo "WebP URL: {$conversion_data['webp_url']}\n";
    }
    if (isset($conversion_data['webp_path'])) {
        echo "WebP Path: {$conversion_data['webp_path']}\n";
    }
} else {
    echo "No conversion data found\n";
}

// Check if any filters are active
echo "\n=== ACTIVE FILTERS ===\n";
global $wp_filter;

$url_filters = array(
    'wp_get_attachment_url',
    'wp_get_attachment_thumb_url', 
    'wp_get_attachment_image_url',
    'wp_prepare_attachment_for_js'
);

foreach ($url_filters as $filter_name) {
    if (isset($wp_filter[$filter_name])) {
        echo "{$filter_name}: " . count($wp_filter[$filter_name]->callbacks) . " callbacks\n";
        foreach ($wp_filter[$filter_name]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && is_object($callback['function'][0])) {
                    $class_name = get_class($callback['function'][0]);
                    $method_name = $callback['function'][1];
                    echo "  Priority {$priority}: {$class_name}::{$method_name}\n";
                } else {
                    echo "  Priority {$priority}: " . print_r($callback['function'], true) . "\n";
                }
            }
        }
    } else {
        echo "{$filter_name}: No callbacks\n";
    }
}

// Test URL generation with filters temporarily removed
echo "\n=== URL WITHOUT FILTERS ===\n";

// Remove all our filters temporarily
remove_all_filters('wp_get_attachment_url');
remove_all_filters('wp_get_attachment_thumb_url');
remove_all_filters('wp_get_attachment_image_url');

$clean_url = wp_get_attachment_url($attachment_id);
echo "URL without filters: {$clean_url}\n";

// Check the database directly
echo "\n=== DATABASE CHECK ===\n";
$post_data = $wpdb->get_row($wpdb->prepare("
    SELECT post_title, post_name, guid, post_content, post_excerpt
    FROM {$wpdb->posts} 
    WHERE ID = %d
", $attachment_id));

echo "Post name: {$post_data->post_name}\n";
echo "GUID: {$post_data->guid}\n";
echo "Post content: {$post_data->post_content}\n";
echo "Post excerpt: {$post_data->post_excerpt}\n";

// Check attachment metadata in database
$meta_data = $wpdb->get_results($wpdb->prepare("
    SELECT meta_key, meta_value
    FROM {$wpdb->postmeta}
    WHERE post_id = %d
    AND meta_key IN ('_wp_attached_file', '_wp_attachment_metadata')
", $attachment_id));

foreach ($meta_data as $meta) {
    echo "Meta {$meta->meta_key}: {$meta->meta_value}\n";
}
?>
