<?php
/**
 * CDN Integration Module
 * 
 * Provides comprehensive CDN integration with multiple providers including
 * Cloudflare, BunnyCDN, KeyCDN, Amazon CloudFront, Sucuri, and Fastly
 * 
 * @package Redco_Optimizer
 * @subpackage CDN_Integration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_CDN_Integration {

    /**
     * CDN provider
     */
    private $cdn_provider;

    /**
     * CDN settings
     */
    private $cdn_settings;

    /**
     * Supported file types for CDN
     */
    private $supported_file_types = array(
        'images' => array('jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'ico'),
        'css' => array('css'),
        'js' => array('js'),
        'fonts' => array('woff', 'woff2', 'ttf', 'eot', 'otf'),
        'media' => array('mp4', 'webm', 'ogg', 'mp3', 'wav'),
        'documents' => array('pdf', 'doc', 'docx', 'zip', 'rar')
    );

    /**
     * Constructor
     */
    public function __construct() {
        // Always register auto-save handler for settings page
        add_action('wp_ajax_redco_save_cdn_settings', array($this, 'ajax_save_cdn_settings'));

        if (redco_is_module_enabled('cdn-integration')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings
     */
    private function load_settings() {
        $defaults = Redco_Config::get_module_defaults('cdn-integration');
        $this->cdn_settings = redco_get_module_option('cdn-integration', 'settings', $defaults);
        $this->cdn_provider = isset($this->cdn_settings['provider']) ? $this->cdn_settings['provider'] : '';
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Only initialize if CDN is enabled and configured
        if (empty($this->cdn_provider) || !isset($this->cdn_settings['enabled']) || !$this->cdn_settings['enabled']) {
            return;
        }

        // URL rewriting hooks
        add_action('init', array($this, 'start_url_rewriting'), 1);
        
        // Admin hooks
        add_action('wp_ajax_redco_test_cdn_connection', array($this, 'ajax_test_cdn_connection'));
        add_action('wp_ajax_redco_purge_cdn_cache', array($this, 'ajax_purge_cdn_cache'));
        add_action('wp_ajax_redco_sync_cdn_files', array($this, 'ajax_sync_cdn_files'));

        // Clear CDN cache when content is updated
        add_action('save_post', array($this, 'clear_cdn_cache_on_update'));
        add_action('wp_update_attachment_metadata', array($this, 'sync_new_media_to_cdn'), 10, 2);

        // Add CDN headers
        add_action('wp_head', array($this, 'add_cdn_preconnect_headers'), 1);
    }

    /**
     * Start URL rewriting for CDN
     */
    public function start_url_rewriting() {
        if (!$this->should_rewrite_urls()) {
            return;
        }

        // Start output buffering to rewrite URLs
        ob_start(array($this, 'rewrite_urls_in_content'));
    }

    /**
     * Check if URLs should be rewritten
     */
    private function should_rewrite_urls() {
        // Skip for admin, AJAX, cron
        if (is_admin() || wp_doing_ajax() || wp_doing_cron()) {
            return false;
        }

        // Skip for logged-in users if configured
        if (isset($this->cdn_settings['skip_logged_in']) && $this->cdn_settings['skip_logged_in'] && is_user_logged_in()) {
            return false;
        }

        // Skip for specific pages if configured
        if (isset($this->cdn_settings['excluded_pages']) && is_array($this->cdn_settings['excluded_pages'])) {
            $current_page_id = get_queried_object_id();
            if (in_array($current_page_id, $this->cdn_settings['excluded_pages'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Rewrite URLs in content to use CDN
     */
    public function rewrite_urls_in_content($content) {
        if (empty($content) || strlen($content) < 255) {
            return $content;
        }

        $cdn_url = $this->get_cdn_url();
        if (empty($cdn_url)) {
            return $content;
        }

        $site_url = home_url();
        $upload_dir = wp_upload_dir();
        $upload_url = $upload_dir['baseurl'];

        // Get enabled file types
        $enabled_types = $this->get_enabled_file_types();
        if (empty($enabled_types)) {
            return $content;
        }

        // Create regex pattern for file extensions
        $extensions = array();
        foreach ($enabled_types as $type) {
            if (isset($this->supported_file_types[$type])) {
                $extensions = array_merge($extensions, $this->supported_file_types[$type]);
            }
        }

        if (empty($extensions)) {
            return $content;
        }

        $extensions_pattern = implode('|', array_map('preg_quote', $extensions));

        // Rewrite URLs in various contexts
        $patterns = array(
            // Images in img tags
            '/(<img[^>]+src=["\'])(' . preg_quote($site_url, '/') . '[^"\']*\.(?:' . $extensions_pattern . '))(["\'][^>]*>)/i',
            // CSS files in link tags
            '/(<link[^>]+href=["\'])(' . preg_quote($site_url, '/') . '[^"\']*\.css)(["\'][^>]*>)/i',
            // JS files in script tags
            '/(<script[^>]+src=["\'])(' . preg_quote($site_url, '/') . '[^"\']*\.js)(["\'][^>]*>)/i',
            // Background images in CSS
            '/(background(?:-image)?:\s*url\(["\']?)(' . preg_quote($site_url, '/') . '[^"\']*\.(?:' . $extensions_pattern . '))(["\']?\))/i',
            // Font files
            '/(@font-face[^}]*url\(["\']?)(' . preg_quote($site_url, '/') . '[^"\']*\.(?:woff2?|ttf|eot|otf))(["\']?\))/i'
        );

        foreach ($patterns as $pattern) {
            $content = preg_replace_callback($pattern, function($matches) use ($cdn_url, $site_url) {
                $full_url = $matches[2];
                $cdn_url_replaced = str_replace($site_url, $cdn_url, $full_url);
                return $matches[1] . $cdn_url_replaced . $matches[3];
            }, $content);
        }

        return $content;
    }

    /**
     * Get CDN URL based on provider
     */
    private function get_cdn_url() {
        switch ($this->cdn_provider) {
            case 'cloudflare':
                return isset($this->cdn_settings['cloudflare_zone_url']) ? $this->cdn_settings['cloudflare_zone_url'] : '';
            
            case 'bunnycdn':
                return isset($this->cdn_settings['bunnycdn_pull_zone_url']) ? $this->cdn_settings['bunnycdn_pull_zone_url'] : '';
            
            case 'keycdn':
                return isset($this->cdn_settings['keycdn_zone_url']) ? $this->cdn_settings['keycdn_zone_url'] : '';
            
            case 'amazon_cloudfront':
                return isset($this->cdn_settings['cloudfront_distribution_url']) ? $this->cdn_settings['cloudfront_distribution_url'] : '';
            
            case 'sucuri':
                return isset($this->cdn_settings['sucuri_cdn_url']) ? $this->cdn_settings['sucuri_cdn_url'] : '';
            
            case 'fastly':
                return isset($this->cdn_settings['fastly_service_url']) ? $this->cdn_settings['fastly_service_url'] : '';
            
            case 'custom':
                return isset($this->cdn_settings['custom_cdn_url']) ? $this->cdn_settings['custom_cdn_url'] : '';
            
            default:
                return '';
        }
    }

    /**
     * Get enabled file types for CDN
     */
    private function get_enabled_file_types() {
        $enabled_types = array();
        
        foreach (array_keys($this->supported_file_types) as $type) {
            if (isset($this->cdn_settings['enable_' . $type]) && $this->cdn_settings['enable_' . $type]) {
                $enabled_types[] = $type;
            }
        }

        return $enabled_types;
    }

    /**
     * Add CDN preconnect headers
     */
    public function add_cdn_preconnect_headers() {
        $cdn_url = $this->get_cdn_url();
        if (empty($cdn_url)) {
            return;
        }

        $parsed_url = parse_url($cdn_url);
        if (isset($parsed_url['host'])) {
            echo '<link rel="dns-prefetch" href="//' . $parsed_url['host'] . '">' . "\n";
            echo '<link rel="preconnect" href="//' . $parsed_url['host'] . '" crossorigin>' . "\n";
        }
    }

    /**
     * Test CDN connection
     */
    public function test_cdn_connection() {
        $cdn_url = $this->get_cdn_url();
        if (empty($cdn_url)) {
            return array(
                'success' => false,
                'message' => 'CDN URL not configured'
            );
        }

        // Test with a simple request
        $test_url = rtrim($cdn_url, '/') . '/wp-includes/js/jquery/jquery.min.js';
        $response = wp_remote_get($test_url, array(
            'timeout' => 10,
            'headers' => array(
                'User-Agent' => 'Redco-Optimizer-CDN-Test/1.0'
            )
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => 'Connection failed: ' . $response->get_error_message()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code === 200) {
            return array(
                'success' => true,
                'message' => 'CDN connection successful',
                'response_time' => $this->get_response_time($response)
            );
        } else {
            return array(
                'success' => false,
                'message' => 'CDN returned HTTP ' . $response_code
            );
        }
    }

    /**
     * Get response time from HTTP response
     */
    private function get_response_time($response) {
        $headers = wp_remote_retrieve_headers($response);
        if (isset($headers['x-response-time'])) {
            return $headers['x-response-time'];
        }
        return 'N/A';
    }

    /**
     * Purge CDN cache
     */
    public function purge_cdn_cache($urls = array()) {
        switch ($this->cdn_provider) {
            case 'cloudflare':
                return $this->purge_cloudflare_cache($urls);
            case 'bunnycdn':
                return $this->purge_bunnycdn_cache($urls);
            case 'keycdn':
                return $this->purge_keycdn_cache($urls);
            case 'amazon_cloudfront':
                return $this->purge_cloudfront_cache($urls);
            case 'sucuri':
                return $this->purge_sucuri_cache($urls);
            case 'fastly':
                return $this->purge_fastly_cache($urls);
            default:
                return array(
                    'success' => false,
                    'message' => 'CDN provider not supported for cache purging'
                );
        }
    }

    /**
     * Purge Cloudflare cache
     */
    private function purge_cloudflare_cache($urls = array()) {
        $zone_id = isset($this->cdn_settings['cloudflare_zone_id']) ? $this->cdn_settings['cloudflare_zone_id'] : '';
        $api_token = isset($this->cdn_settings['cloudflare_api_token']) ? $this->cdn_settings['cloudflare_api_token'] : '';

        if (empty($zone_id) || empty($api_token)) {
            return array(
                'success' => false,
                'message' => 'Cloudflare credentials not configured'
            );
        }

        $api_url = "https://api.cloudflare.com/client/v4/zones/{$zone_id}/purge_cache";

        $data = array();
        if (!empty($urls)) {
            $data['files'] = $urls;
        } else {
            $data['purge_everything'] = true;
        }

        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_token,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => 'Request failed: ' . $response->get_error_message()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_data = json_decode($response_body, true);

        if ($response_code === 200 && isset($response_data['success']) && $response_data['success']) {
            return array(
                'success' => true,
                'message' => 'Cloudflare cache purged successfully'
            );
        } else {
            $error_message = isset($response_data['errors'][0]['message']) ? $response_data['errors'][0]['message'] : 'Unknown error';
            return array(
                'success' => false,
                'message' => 'Cloudflare API error: ' . $error_message
            );
        }
    }

    /**
     * Purge BunnyCDN cache
     */
    private function purge_bunnycdn_cache($urls = array()) {
        $api_key = isset($this->cdn_settings['bunnycdn_api_key']) ? $this->cdn_settings['bunnycdn_api_key'] : '';
        $pull_zone_id = isset($this->cdn_settings['bunnycdn_pull_zone_id']) ? $this->cdn_settings['bunnycdn_pull_zone_id'] : '';

        if (empty($api_key) || empty($pull_zone_id)) {
            return array(
                'success' => false,
                'message' => 'BunnyCDN credentials not configured'
            );
        }

        if (!empty($urls)) {
            // Purge specific URLs
            $success_count = 0;
            $errors = array();

            foreach ($urls as $url) {
                $api_url = "https://api.bunny.net/pullzone/{$pull_zone_id}/purgeCache";
                $response = wp_remote_post($api_url, array(
                    'headers' => array(
                        'AccessKey' => $api_key,
                        'Content-Type' => 'application/json'
                    ),
                    'body' => json_encode(array('url' => $url)),
                    'timeout' => 30
                ));

                if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
                    $success_count++;
                } else {
                    $errors[] = $url;
                }
            }

            if ($success_count === count($urls)) {
                return array(
                    'success' => true,
                    'message' => 'BunnyCDN cache purged successfully for ' . $success_count . ' URLs'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to purge ' . count($errors) . ' URLs'
                );
            }
        } else {
            // Purge all cache
            $api_url = "https://api.bunny.net/pullzone/{$pull_zone_id}/purgeCache";
            $response = wp_remote_post($api_url, array(
                'headers' => array(
                    'AccessKey' => $api_key
                ),
                'timeout' => 30
            ));

            if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
                return array(
                    'success' => true,
                    'message' => 'BunnyCDN cache purged successfully'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to purge BunnyCDN cache'
                );
            }
        }
    }

    /**
     * Purge KeyCDN cache
     */
    private function purge_keycdn_cache($urls = array()) {
        $api_key = isset($this->cdn_settings['keycdn_api_key']) ? $this->cdn_settings['keycdn_api_key'] : '';
        $zone_id = isset($this->cdn_settings['keycdn_zone_id']) ? $this->cdn_settings['keycdn_zone_id'] : '';

        if (empty($api_key) || empty($zone_id)) {
            return array(
                'success' => false,
                'message' => 'KeyCDN credentials not configured'
            );
        }

        $api_url = "https://api.keycdn.com/zones/purge/{$zone_id}.json";

        $data = array();
        if (!empty($urls)) {
            $data['urls'] = $urls;
        } else {
            $data['purgeall'] = true;
        }

        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode($api_key . ':'),
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            return array(
                'success' => true,
                'message' => 'KeyCDN cache purged successfully'
            );
        } else {
            return array(
                'success' => false,
                'message' => 'Failed to purge KeyCDN cache'
            );
        }
    }

    /**
     * Purge Amazon CloudFront cache
     */
    private function purge_cloudfront_cache($urls = array()) {
        // CloudFront invalidation requires AWS SDK
        // This is a simplified implementation
        $distribution_id = isset($this->cdn_settings['cloudfront_distribution_id']) ? $this->cdn_settings['cloudfront_distribution_id'] : '';
        $access_key = isset($this->cdn_settings['aws_access_key']) ? $this->cdn_settings['aws_access_key'] : '';
        $secret_key = isset($this->cdn_settings['aws_secret_key']) ? $this->cdn_settings['aws_secret_key'] : '';

        if (empty($distribution_id) || empty($access_key) || empty($secret_key)) {
            return array(
                'success' => false,
                'message' => 'CloudFront credentials not configured'
            );
        }

        // Note: Full CloudFront integration would require AWS SDK
        // This is a placeholder for the implementation
        return array(
            'success' => false,
            'message' => 'CloudFront integration requires AWS SDK (coming soon)'
        );
    }

    /**
     * Purge Sucuri cache
     */
    private function purge_sucuri_cache($urls = array()) {
        $api_key = isset($this->cdn_settings['sucuri_api_key']) ? $this->cdn_settings['sucuri_api_key'] : '';
        $api_secret = isset($this->cdn_settings['sucuri_api_secret']) ? $this->cdn_settings['sucuri_api_secret'] : '';

        if (empty($api_key) || empty($api_secret)) {
            return array(
                'success' => false,
                'message' => 'Sucuri credentials not configured'
            );
        }

        $api_url = 'https://waf.sucuri.net/api/v2/cache/clear';

        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'X-API-KEY' => $api_key,
                'X-API-SECRET' => $api_secret
            ),
            'timeout' => 30
        ));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            return array(
                'success' => true,
                'message' => 'Sucuri cache purged successfully'
            );
        } else {
            return array(
                'success' => false,
                'message' => 'Failed to purge Sucuri cache'
            );
        }
    }

    /**
     * Purge Fastly cache
     */
    private function purge_fastly_cache($urls = array()) {
        $api_token = isset($this->cdn_settings['fastly_api_token']) ? $this->cdn_settings['fastly_api_token'] : '';
        $service_id = isset($this->cdn_settings['fastly_service_id']) ? $this->cdn_settings['fastly_service_id'] : '';

        if (empty($api_token) || empty($service_id)) {
            return array(
                'success' => false,
                'message' => 'Fastly credentials not configured'
            );
        }

        if (!empty($urls)) {
            // Purge specific URLs
            $success_count = 0;
            $errors = array();

            foreach ($urls as $url) {
                $api_url = "https://api.fastly.com/purge/{$url}";
                $response = wp_remote_post($api_url, array(
                    'headers' => array(
                        'Fastly-Token' => $api_token,
                        'Accept' => 'application/json'
                    ),
                    'timeout' => 30
                ));

                if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
                    $success_count++;
                } else {
                    $errors[] = $url;
                }
            }

            if ($success_count === count($urls)) {
                return array(
                    'success' => true,
                    'message' => 'Fastly cache purged successfully for ' . $success_count . ' URLs'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to purge ' . count($errors) . ' URLs'
                );
            }
        } else {
            // Purge all cache
            $api_url = "https://api.fastly.com/service/{$service_id}/purge_all";
            $response = wp_remote_post($api_url, array(
                'headers' => array(
                    'Fastly-Token' => $api_token,
                    'Accept' => 'application/json'
                ),
                'timeout' => 30
            ));

            if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
                return array(
                    'success' => true,
                    'message' => 'Fastly cache purged successfully'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to purge Fastly cache'
                );
            }
        }
    }

    /**
     * Clear CDN cache when content is updated
     */
    public function clear_cdn_cache_on_update($post_id) {
        if (wp_is_post_revision($post_id)) {
            return;
        }

        $post_url = get_permalink($post_id);
        if ($post_url) {
            $this->purge_cdn_cache(array($post_url));
        }
    }

    /**
     * Sync new media to CDN
     */
    public function sync_new_media_to_cdn($metadata, $attachment_id) {
        // This would implement media file syncing to CDN
        // For now, we'll just trigger a cache warm-up
        $attachment_url = wp_get_attachment_url($attachment_id);
        if ($attachment_url) {
            // Warm up the CDN cache for the new file
            wp_remote_get($attachment_url, array('timeout' => 5));
        }

        return $metadata;
    }

    /**
     * AJAX handler for testing CDN connection
     */
    public function ajax_test_cdn_connection() {
        check_ajax_referer('redco_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        $result = $this->test_cdn_connection();

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * AJAX handler for purging CDN cache
     */
    public function ajax_purge_cdn_cache() {
        check_ajax_referer('redco_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        $urls = isset($_POST['urls']) ? array_map('esc_url_raw', $_POST['urls']) : array();
        $result = $this->purge_cdn_cache($urls);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * AJAX handler for syncing files to CDN
     */
    public function ajax_sync_cdn_files() {
        check_ajax_referer('redco_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        // This would implement bulk file syncing
        wp_send_json_success(array(
            'message' => 'CDN file sync initiated (feature coming soon)'
        ));
    }

    /**
     * AJAX handler for auto-saving CDN settings
     */
    public function ajax_save_cdn_settings() {
        check_ajax_referer('redco_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        // Get and sanitize settings data
        $settings_data = isset($_POST['settings']) ? $_POST['settings'] : array();

        if (empty($settings_data)) {
            wp_send_json_error(array(
                'message' => __('No settings data provided', 'redco-optimizer')
            ));
        }

        // Sanitize and validate settings
        $sanitized_settings = $this->sanitize_cdn_settings($settings_data);
        $validation_result = $this->validate_cdn_settings($sanitized_settings);

        if (!$validation_result['valid']) {
            wp_send_json_error(array(
                'message' => $validation_result['message']
            ));
        }

        // Save settings using the WordPress options API
        $save_result = redco_update_module_option('cdn-integration', 'settings', $sanitized_settings);

        if ($save_result) {
            // Reload settings to ensure they're current
            $this->load_settings();

            wp_send_json_success(array(
                'message' => __('CDN settings saved successfully', 'redco-optimizer'),
                'settings' => $sanitized_settings
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to save CDN settings', 'redco-optimizer')
            ));
        }
    }

    /**
     * Sanitize CDN settings data
     */
    private function sanitize_cdn_settings($settings) {
        $sanitized = array();

        // Boolean settings
        $boolean_fields = array('enabled', 'skip_logged_in', 'enable_images', 'enable_css', 'enable_js', 'enable_fonts', 'enable_media', 'enable_documents');
        foreach ($boolean_fields as $field) {
            $sanitized[$field] = isset($settings[$field]) ? (bool) $settings[$field] : false;
        }

        // Text/URL fields
        $text_fields = array(
            'provider' => 'sanitize_text_field',
            'cloudflare_zone_id' => 'sanitize_text_field',
            'cloudflare_api_token' => 'sanitize_text_field',
            'cloudflare_zone_url' => 'esc_url_raw',
            'bunnycdn_api_key' => 'sanitize_text_field',
            'bunnycdn_pull_zone_id' => 'sanitize_text_field',
            'bunnycdn_pull_zone_url' => 'esc_url_raw',
            'keycdn_api_key' => 'sanitize_text_field',
            'keycdn_zone_id' => 'sanitize_text_field',
            'keycdn_zone_url' => 'esc_url_raw',
            'cloudfront_distribution_id' => 'sanitize_text_field',
            'cloudfront_distribution_url' => 'esc_url_raw',
            'sucuri_api_key' => 'sanitize_text_field',
            'sucuri_api_secret' => 'sanitize_text_field',
            'sucuri_cdn_url' => 'esc_url_raw',
            'fastly_api_token' => 'sanitize_text_field',
            'fastly_service_id' => 'sanitize_text_field',
            'fastly_service_url' => 'esc_url_raw',
            'custom_cdn_url' => 'esc_url_raw'
        );

        foreach ($text_fields as $field => $sanitize_function) {
            if (isset($settings[$field])) {
                $sanitized[$field] = call_user_func($sanitize_function, $settings[$field]);
            }
        }

        return $sanitized;
    }

    /**
     * Validate CDN settings
     */
    private function validate_cdn_settings($settings) {
        $result = array('valid' => true, 'message' => '');

        // If CDN is enabled, validate provider-specific settings
        if (isset($settings['enabled']) && $settings['enabled']) {
            $provider = isset($settings['provider']) ? $settings['provider'] : '';

            if (empty($provider)) {
                return array(
                    'valid' => false,
                    'message' => __('Please select a CDN provider when CDN is enabled', 'redco-optimizer')
                );
            }

            // Validate provider-specific required fields
            switch ($provider) {
                case 'cloudflare':
                    if (empty($settings['cloudflare_zone_id']) || empty($settings['cloudflare_api_token'])) {
                        return array(
                            'valid' => false,
                            'message' => __('Cloudflare Zone ID and API Token are required', 'redco-optimizer')
                        );
                    }
                    break;

                case 'bunnycdn':
                    if (empty($settings['bunnycdn_api_key']) || empty($settings['bunnycdn_pull_zone_id'])) {
                        return array(
                            'valid' => false,
                            'message' => __('BunnyCDN API Key and Pull Zone ID are required', 'redco-optimizer')
                        );
                    }
                    break;

                case 'keycdn':
                    if (empty($settings['keycdn_api_key']) || empty($settings['keycdn_zone_id'])) {
                        return array(
                            'valid' => false,
                            'message' => __('KeyCDN API Key and Zone ID are required', 'redco-optimizer')
                        );
                    }
                    break;

                case 'amazon_cloudfront':
                    if (empty($settings['cloudfront_distribution_id'])) {
                        return array(
                            'valid' => false,
                            'message' => __('CloudFront Distribution ID is required', 'redco-optimizer')
                        );
                    }
                    break;

                case 'sucuri':
                    if (empty($settings['sucuri_api_key']) || empty($settings['sucuri_api_secret'])) {
                        return array(
                            'valid' => false,
                            'message' => __('Sucuri API Key and API Secret are required', 'redco-optimizer')
                        );
                    }
                    break;

                case 'fastly':
                    if (empty($settings['fastly_api_token']) || empty($settings['fastly_service_id'])) {
                        return array(
                            'valid' => false,
                            'message' => __('Fastly API Token and Service ID are required', 'redco-optimizer')
                        );
                    }
                    break;

                case 'custom':
                    if (empty($settings['custom_cdn_url'])) {
                        return array(
                            'valid' => false,
                            'message' => __('Custom CDN URL is required', 'redco-optimizer')
                        );
                    }
                    break;
            }
        }

        return $result;
    }

    /**
     * Get CDN statistics
     */
    public function get_cdn_stats() {
        $stats = array(
            'provider' => $this->cdn_provider,
            'enabled' => isset($this->cdn_settings['enabled']) ? $this->cdn_settings['enabled'] : false,
            'cdn_url' => $this->get_cdn_url(),
            'enabled_file_types' => $this->get_enabled_file_types(),
            'total_requests' => get_option('redco_cdn_requests', 0),
            'cache_hits' => get_option('redco_cdn_cache_hits', 0),
            'bandwidth_saved' => get_option('redco_cdn_bandwidth_saved', 0)
        );

        return $stats;
    }
}

// Initialize the module
function redco_init_cdn_integration() {
    if (redco_is_module_enabled('cdn-integration')) {
        new Redco_CDN_Integration();
    }
}
add_action('init', 'redco_init_cdn_integration', 10);
