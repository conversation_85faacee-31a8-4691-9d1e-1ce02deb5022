<?php
/**
 * Asset Optimization Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$asset_optimization = new Redco_Asset_Optimization();
$is_enabled = redco_is_module_enabled('asset-optimization');

// Get current settings using centralized configuration
$defaults = Redco_Config::get_module_defaults('asset-optimization');
$settings = redco_get_module_option('asset-optimization', 'settings', $defaults);

// Get optimization statistics - only if module is enabled
$optimization_stats = array(
    'files_optimized' => 0,
    'bytes_saved' => '0 B',
    'css_files' => 0,
    'js_files' => 0,
    'compression_ratio' => 0
);
$trending_files = array();
$optimization_tips = array();

if ($is_enabled && class_exists('Redco_Asset_Optimization')) {
    $optimization_stats = $asset_optimization->get_optimization_stats();
    $trending_files = $asset_optimization->get_trending_files(5);
    $optimization_tips = $asset_optimization->get_optimization_tips();
}
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-module-tab" data-module="asset-optimization">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('Asset Optimization', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-performance"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Asset Optimization', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Optimize CSS, JavaScript, and other assets for faster loading times and better performance', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if ($optimization_stats['files_optimized'] > 0): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-media-code"></span>
                                    <?php _e('Files Optimized', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($optimization_stats['compression_ratio'] > 30): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-performance"></span>
                                    <?php _e('High Compression', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="clear-optimization-cache" data-redco-action="clear_optimization_cache">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Clear Cache', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="regenerate-critical-css" data-redco-action="regenerate_critical_css">
                                <span class="dashicons dashicons-update"></span>
                                <?php _e('Regenerate CSS', 'redco-optimizer'); ?>
                            </button>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="header-action-btn">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Diagnose', 'redco-optimizer'); ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo number_format($optimization_stats['files_optimized']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Files', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $optimization_stats['compression_ratio']; ?>%
                            </div>
                            <div class="header-metric-label"><?php _e('Saved', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $optimization_stats['bytes_saved']; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Size', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="asset-optimization">
                    <!-- CSS/JS Minification Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-editor-code"></span>
                                <?php _e('CSS & JavaScript Minification', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Minify CSS and JavaScript files to reduce file sizes and improve loading speed. Configure which files to optimize and which to exclude.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Current Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="minification-impact"><?php _e('High Performance', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[minify_css]" value="1" <?php checked($settings['minify_css']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Minify CSS Files', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Automatically minify CSS files to reduce file sizes and improve loading speed.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">📦 Smaller file sizes</span>
                                            <span class="benefit-item">⚡ Faster loading</span>
                                            <span class="benefit-item">🔄 Automatic processing</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[minify_js]" value="1" <?php checked($settings['minify_js']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Minify JavaScript Files', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Automatically minify JavaScript files to reduce file sizes and improve performance.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">📦 Smaller scripts</span>
                                            <span class="benefit-item">⚡ Faster execution</span>
                                            <span class="benefit-item">🔄 Safe processing</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[minify_inline]" value="1" <?php checked($settings['minify_inline']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Minify Inline CSS/JS', 'redco-optimizer'); ?></strong>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Minify CSS and JavaScript code embedded directly in HTML pages.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">📄 Cleaner HTML</span>
                                            <span class="benefit-item">⚡ Reduced page size</span>
                                            <span class="benefit-item">🔄 Inline optimization</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Critical Resource Optimization Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-performance"></span>
                                <?php _e('Critical Resource Optimization', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Optimize critical above-the-fold resources for faster initial page rendering. Includes critical CSS inlining and resource loading optimization.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Current Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="critical-impact"><?php _e('High Performance', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[critical_css]" value="1" <?php checked($settings['critical_css']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Critical CSS Inlining', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Inline critical above-the-fold CSS for faster initial page rendering and improved Core Web Vitals.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">⚡ Faster rendering</span>
                                            <span class="benefit-item">📊 Better Core Web Vitals</span>
                                            <span class="benefit-item">🎯 Above-the-fold optimization</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[defer_non_critical]" value="1" <?php checked($settings['defer_non_critical']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Defer Non-Critical CSS', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Load non-critical CSS asynchronously to improve page speed and reduce render-blocking resources.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">🚀 Faster page speed</span>
                                            <span class="benefit-item">📈 Reduced blocking</span>
                                            <span class="benefit-item">⚡ Async loading</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[optimize_js]" value="1" <?php checked($settings['optimize_js']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('JavaScript Loading Optimization', 'redco-optimizer'); ?></strong>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Optimize JavaScript loading with async and defer attributes for better performance.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">⚡ Non-blocking scripts</span>
                                            <span class="benefit-item">🔄 Async execution</span>
                                            <span class="benefit-item">📈 Better performance</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[optimize_fonts]" value="1" <?php checked($settings['optimize_fonts']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Font Loading Optimization', 'redco-optimizer'); ?></strong>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Optimize font loading with font-display: swap and preconnect hints to prevent layout shifts.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">🔤 Faster font loading</span>
                                            <span class="benefit-item">📐 Reduced layout shifts</span>
                                            <span class="benefit-item">⚡ Preconnect optimization</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resource Hints Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-networking"></span>
                                <?php _e('Resource Hints & Preloading', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Add resource hints to improve loading performance by establishing early connections to external domains and preloading critical resources.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="settings-section">
                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[resource_hints]" value="1" <?php checked($settings['resource_hints']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Enable Resource Hints', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Add DNS prefetch and preconnect hints for external resources to improve loading performance.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">🌐 DNS prefetch</span>
                                            <span class="benefit-item">🔗 Preconnect hints</span>
                                            <span class="benefit-item">⚡ Faster connections</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[preconnect_google_fonts]" value="1" <?php checked($settings['preconnect_google_fonts']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Preconnect to Google Fonts', 'redco-optimizer'); ?></strong>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Add preconnect hints for Google Fonts domains to speed up font loading.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">🔤 Faster fonts</span>
                                            <span class="benefit-item">🌐 Google Fonts optimization</span>
                                            <span class="benefit-item">⚡ Preconnect hints</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="settings[preconnect_analytics]" value="1" <?php checked($settings['preconnect_analytics']); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Preconnect to Analytics', 'redco-optimizer'); ?></strong>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Add preconnect hints for Google Analytics domains to improve tracking performance.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">📊 Analytics optimization</span>
                                            <span class="benefit-item">🌐 Google domains</span>
                                            <span class="benefit-item">⚡ Faster tracking</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Optimization Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Optimization Performance', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-files">
                                <span class="stat-value"><?php echo number_format($optimization_stats['files_optimized']); ?></span>
                                <span class="stat-label"><?php _e('Files Optimized', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-savings">
                                <span class="stat-value"><?php echo esc_html($optimization_stats['bytes_saved']); ?></span>
                                <span class="stat-label"><?php _e('Bytes Saved', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-ratio">
                                <span class="stat-value"><?php echo $optimization_stats['compression_ratio']; ?>%</span>
                                <span class="stat-label"><?php _e('Compression Ratio', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-css">
                                <span class="stat-value"><?php echo number_format($optimization_stats['css_files']); ?></span>
                                <span class="stat-label"><?php _e('CSS Files', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-js">
                                <span class="stat-value"><?php echo number_format($optimization_stats['js_files']); ?></span>
                                <span class="stat-label"><?php _e('JS Files', 'redco-optimizer'); ?></span>
                            </div>
                        </div>

                        <div class="optimization-performance-summary">
                            <div class="performance-indicator performance-<?php echo $optimization_stats['compression_ratio'] >= 30 ? 'excellent' : ($optimization_stats['compression_ratio'] >= 20 ? 'good' : ($optimization_stats['compression_ratio'] >= 10 ? 'fair' : 'poor')); ?>">
                                <span class="indicator-icon dashicons dashicons-<?php echo $optimization_stats['compression_ratio'] >= 30 ? 'yes-alt' : ($optimization_stats['compression_ratio'] >= 20 ? 'thumbs-up' : ($optimization_stats['compression_ratio'] >= 10 ? 'warning' : 'dismiss')); ?>"></span>
                                <span class="indicator-text">
                                    <?php
                                    if ($optimization_stats['compression_ratio'] >= 30) {
                                        _e('Excellent Optimization', 'redco-optimizer');
                                    } elseif ($optimization_stats['compression_ratio'] >= 20) {
                                        _e('Good Optimization', 'redco-optimizer');
                                    } elseif ($optimization_stats['compression_ratio'] >= 10) {
                                        _e('Fair Optimization', 'redco-optimizer');
                                    } else {
                                        _e('Needs Improvement', 'redco-optimizer');
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Quick Actions', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="sidebar-actions">
                            <button type="button" class="sidebar-action-btn" id="clear-optimization-cache-sidebar" data-redco-action="clear_optimization_cache">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Clear Cache', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="sidebar-action-btn" id="regenerate-critical-css-sidebar" data-redco-action="regenerate_critical_css">
                                <span class="dashicons dashicons-update"></span>
                                <?php _e('Regenerate CSS', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="sidebar-action-btn" id="optimize-all-assets" data-redco-action="optimize_all_assets">
                                <span class="dashicons dashicons-performance"></span>
                                <?php _e('Optimize All', 'redco-optimizer'); ?>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Optimization Tips -->
                <?php if (!empty($optimization_tips)): ?>
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-lightbulb"></span>
                            <?php _e('Optimization Tips', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="optimization-tips">
                            <?php foreach ($optimization_tips as $tip): ?>
                                <div class="tip-item">
                                    <span class="tip-icon dashicons dashicons-<?php echo esc_attr($tip['icon']); ?>"></span>
                                    <div class="tip-content">
                                        <div class="tip-title"><?php echo esc_html($tip['title']); ?></div>
                                        <div class="tip-description"><?php echo esc_html($tip['description']); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
