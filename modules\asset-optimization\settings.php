<?php
/**
 * Asset Optimization Module Settings
 * 
 * Unified settings for CSS/JS minification and critical resource optimization
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$asset_optimization = new Redco_Asset_Optimization();
$is_enabled = redco_is_module_enabled('asset-optimization');

// Get current settings
$settings = redco_get_module_option('asset-optimization', 'settings', array());

// Default settings
$defaults = array(
    'minify_css' => true,
    'minify_js' => true,
    'minify_inline' => true,
    'exclude_css' => array(),
    'exclude_js' => array('jquery-core', 'jquery-migrate'),
    'critical_css' => true,
    'defer_non_critical' => true,
    'optimize_js' => true,
    'optimize_fonts' => true,
    'resource_hints' => true,
    'preconnect_google_fonts' => true,
    'preconnect_analytics' => true,
    'combine_css' => false,
    'combine_js' => false,
    'async_js' => true,
    'defer_js' => true,
    'preload_critical' => true,
    'remove_unused_css' => false,
    'enable_gzip' => true,
    'cache_duration' => 86400,
    'enable_brotli' => false
);

$settings = wp_parse_args($settings, $defaults);

// Get statistics
$stats = array('total_files' => 0, 'bytes_saved' => 0, 'compression_ratio' => 0);
if ($is_enabled && class_exists('Redco_Asset_Optimization')) {
    $stats = $asset_optimization->get_stats();
}

// Get enqueued styles and scripts for exclusion lists
$enqueued_styles = redco_get_enqueued_styles();
$enqueued_scripts = redco_get_enqueued_scripts();
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<div class="redco-optimizer-admin">
    <!-- Enhanced Module Header -->
    <div class="redco-module-header">
        <div class="module-header-content">
            <div class="module-title-section">
                <h1 class="module-title">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Asset Optimization', 'redco-optimizer'); ?>
                    <span class="module-badge unified-badge"><?php _e('Unified Module', 'redco-optimizer'); ?></span>
                </h1>
                <p class="module-description">
                    <?php _e('Unified CSS/JS minification with critical resource optimization. Combines minification, critical CSS, resource hints, and performance optimization in one powerful module.', 'redco-optimizer'); ?>
                </p>
            </div>

            <div class="module-actions">
                <?php if ($is_enabled): ?>
                    <button type="button" class="button button-secondary" id="clear-asset-cache">
                        <span class="dashicons dashicons-trash"></span>
                        <?php _e('Clear Cache', 'redco-optimizer'); ?>
                    </button>
                    <button type="button" class="button button-secondary" id="generate-critical-css">
                        <span class="dashicons dashicons-admin-appearance"></span>
                        <?php _e('Generate Critical CSS', 'redco-optimizer'); ?>
                    </button>
                    <button type="button" class="button button-primary" id="optimize-assets">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Optimize All Assets', 'redco-optimizer'); ?>
                    </button>
                <?php endif; ?>
            </div>
        </div>

        <!-- Module Status Indicator -->
        <div class="module-status-indicator">
            <div class="status-light <?php echo $is_enabled ? 'status-active' : 'status-inactive'; ?>"></div>
            <span class="status-text">
                <?php echo $is_enabled ? __('Active', 'redco-optimizer') : __('Inactive', 'redco-optimizer'); ?>
            </span>
        </div>
    </div>

    <!-- Module Toggle -->
    <div class="redco-module-toggle">
        <div class="toggle-container">
            <label class="toggle-label">
                <input type="checkbox" 
                       class="module-toggle-checkbox" 
                       data-module="asset-optimization" 
                       <?php checked($is_enabled); ?>>
                <span class="toggle-slider"></span>
                <span class="toggle-text">
                    <?php _e('Enable Asset Optimization', 'redco-optimizer'); ?>
                </span>
            </label>
            <p class="toggle-description">
                <?php _e('Enable unified asset optimization including CSS/JS minification, critical CSS, and resource loading optimization.', 'redco-optimizer'); ?>
            </p>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Unified Statistics Dashboard -->
    <div class="redco-stats-section">
        <h2><?php _e('Optimization Statistics', 'redco-optimizer'); ?></h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-icon">
                    <span class="dashicons dashicons-media-document"></span>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?php echo number_format($stats['total_files']); ?></div>
                    <div class="stat-label"><?php _e('Files Optimized', 'redco-optimizer'); ?></div>
                </div>
            </div>

            <div class="stat-item">
                <div class="stat-icon">
                    <span class="dashicons dashicons-download"></span>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?php echo $stats['bytes_saved_formatted']; ?></div>
                    <div class="stat-label"><?php _e('Bytes Saved', 'redco-optimizer'); ?></div>
                </div>
            </div>

            <div class="stat-item">
                <div class="stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?php echo $stats['compression_ratio']; ?>%</div>
                    <div class="stat-label"><?php _e('Compression Ratio', 'redco-optimizer'); ?></div>
                </div>
            </div>

            <div class="stat-item">
                <div class="stat-icon">
                    <span class="dashicons dashicons-database"></span>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?php echo $stats['cache_size']; ?></div>
                    <div class="stat-label"><?php _e('Cache Size', 'redco-optimizer'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="asset-optimization">
                    
                    <!-- CSS/JS Minification Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-editor-code"></span>
                                <?php _e('CSS & JavaScript Minification', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Minify CSS and JavaScript files to reduce file sizes and improve loading speed. Configure which files to optimize and which to exclude.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[minify_css]" value="1" <?php checked($settings['minify_css']); ?>>
                                        <span class="setting-title"><?php _e('Minify CSS Files', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Automatically minify CSS files to reduce file sizes', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[minify_js]" value="1" <?php checked($settings['minify_js']); ?>>
                                        <span class="setting-title"><?php _e('Minify JavaScript Files', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Automatically minify JavaScript files to reduce file sizes', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[minify_inline]" value="1" <?php checked($settings['minify_inline']); ?>>
                                        <span class="setting-title"><?php _e('Minify Inline CSS/JS', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Minify CSS and JavaScript code embedded directly in HTML', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Critical Resource Optimization Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-performance"></span>
                                <?php _e('Critical Resource Optimization', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Optimize critical above-the-fold resources for faster initial page rendering. Includes critical CSS inlining and resource loading optimization.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[critical_css]" value="1" <?php checked($settings['critical_css']); ?>>
                                        <span class="setting-title"><?php _e('Critical CSS Inlining', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Inline critical above-the-fold CSS for faster rendering', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[defer_non_critical]" value="1" <?php checked($settings['defer_non_critical']); ?>>
                                        <span class="setting-title"><?php _e('Defer Non-Critical CSS', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Load non-critical CSS asynchronously to improve page speed', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[optimize_js]" value="1" <?php checked($settings['optimize_js']); ?>>
                                        <span class="setting-title"><?php _e('JavaScript Loading Optimization', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Optimize JavaScript loading with async and defer attributes', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[optimize_fonts]" value="1" <?php checked($settings['optimize_fonts']); ?>>
                                        <span class="setting-title"><?php _e('Font Loading Optimization', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Optimize font loading with font-display: swap and preconnect hints', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resource Hints Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-networking"></span>
                                <?php _e('Resource Hints & Preloading', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Add resource hints to improve loading performance by establishing early connections to external domains and preloading critical resources.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[resource_hints]" value="1" <?php checked($settings['resource_hints']); ?>>
                                        <span class="setting-title"><?php _e('Enable Resource Hints', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Add DNS prefetch and preconnect hints for external resources', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[preconnect_google_fonts]" value="1" <?php checked($settings['preconnect_google_fonts']); ?>>
                                        <span class="setting-title"><?php _e('Preconnect to Google Fonts', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Add preconnect hints for Google Fonts domains', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>

                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" name="settings[preconnect_analytics]" value="1" <?php checked($settings['preconnect_analytics']); ?>>
                                        <span class="setting-title"><?php _e('Preconnect to Analytics', 'redco-optimizer'); ?></span>
                                        <span class="help-tooltip" data-tooltip="<?php esc_attr_e('Add preconnect hints for Google Analytics domains', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.unified-badge {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.setting-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    cursor: pointer;
}

.setting-title {
    font-weight: 500;
    color: #333;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Clear cache button
    $('#clear-asset-cache').on('click', function() {
        const button = $(this);
        button.prop('disabled', true).text('Clearing...');
        
        $.post(ajaxurl, {
            action: 'redco_clear_asset_cache',
            nonce: '<?php echo wp_create_nonce('redco_asset_optimization_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                showNotification(response.data.message, 'success');
            } else {
                showNotification('Failed to clear cache', 'error');
            }
        }).always(function() {
            button.prop('disabled', false).text('Clear Cache');
        });
    });

    // Generate critical CSS button
    $('#generate-critical-css').on('click', function() {
        const button = $(this);
        button.prop('disabled', true).text('Generating...');
        
        $.post(ajaxurl, {
            action: 'redco_generate_critical_css',
            url: window.location.origin,
            nonce: '<?php echo wp_create_nonce('redco_asset_optimization_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                showNotification(response.data.message, 'success');
            } else {
                showNotification(response.data.message || 'Failed to generate critical CSS', 'error');
            }
        }).always(function() {
            button.prop('disabled', false).text('Generate Critical CSS');
        });
    });

    // Optimize assets button
    $('#optimize-assets').on('click', function() {
        const button = $(this);
        button.prop('disabled', true).text('Optimizing...');
        
        $.post(ajaxurl, {
            action: 'redco_optimize_assets',
            nonce: '<?php echo wp_create_nonce('redco_asset_optimization_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                showNotification(response.data.message, 'success');
                // Refresh page to show updated stats
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification('Failed to optimize assets', 'error');
            }
        }).always(function() {
            button.prop('disabled', false).text('Optimize All Assets');
        });
    });

    function showNotification(message, type) {
        // Use existing notification system if available
        if (typeof redcoShowToast === 'function') {
            redcoShowToast(message, type);
        } else {
            alert(message);
        }
    }
});
</script>
