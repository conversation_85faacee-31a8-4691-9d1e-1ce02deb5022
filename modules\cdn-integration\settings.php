<?php
/**
 * CDN Integration Module Settings - Phase 4 Clean
 *
 * @package Redco_Optimizer
 * @subpackage CDN_Integration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load Phase 4 CDN classes
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-cdn-provider-manager.php';

// Get module instance and settings
$is_enabled = redco_is_module_enabled('cdn-integration');
$active_provider = redco_get_module_option('cdn-integration', 'active_provider', '');
$cdn_enabled = redco_get_module_option('cdn-integration', 'cdn_enabled', false);
$current_settings = redco_get_module_option('cdn-integration', 'cdn_settings', array());

// Get all available providers
$providers = Redco_CDN_Provider_Manager::get_providers();

// Get provider status for each provider
$provider_statuses = array();
foreach ($providers as $provider_name => $provider_data) {
    $provider_statuses[$provider_name] = Redco_CDN_Provider_Manager::get_provider_status($provider_name);
}

// Get recommended provider
$recommended_provider = Redco_CDN_Provider_Manager::get_recommended_provider();

// Detect existing CDN
$existing_cdn = Redco_CDN_Provider_Manager::detect_existing_cdn();

// Get CDN statistics
$cdn_stats = array(
    'enabled' => $cdn_enabled && !empty($active_provider),
    'total_requests' => 0,
    'cache_hits' => 0,
    'bandwidth_saved' => 0
);

if ($is_enabled && !empty($active_provider)) {
    $provider_instance = Redco_CDN_Provider_Manager::get_provider_instance($active_provider);
    if ($provider_instance) {
        $stats = $provider_instance->get_cache_stats();
        $cdn_stats = array_merge($cdn_stats, $stats);
    }
}

// Calculate cache hit ratio for display
$stats = array(
    'cache_hit_ratio' => $cdn_stats['total_requests'] > 0 ?
        round(($cdn_stats['cache_hits'] / $cdn_stats['total_requests']) * 100, 1) : 0
);
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<style>
/* Phase 4: CDN Provider Grid Styling */
.cdn-provider-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.cdn-provider-card {
    background: #fff;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.cdn-provider-card:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
    transform: translateY(-2px);
}

.cdn-provider-card.active {
    border-color: #4CAF50;
    background: #f8fff8;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
}

.cdn-provider-card.recommended::before {
    content: "⭐ Recommended";
    position: absolute;
    top: -10px;
    right: 15px;
    background: #4CAF50;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.provider-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.provider-logo .provider-icon {
    font-size: 32px;
    color: #4CAF50;
}

.provider-info h4 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.provider-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.provider-status.status-configured {
    background: #d4edda;
    color: #155724;
}

.provider-status.status-not_configured {
    background: #fff3cd;
    color: #856404;
}

.provider-status.status-active {
    background: #d1ecf1;
    color: #0c5460;
}

.provider-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.provider-features li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    font-size: 13px;
    color: #555;
}

.provider-features .dashicons {
    color: #4CAF50;
    font-size: 14px;
}

.provider-metrics {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    padding: 10px 0;
    border-top: 1px solid #eee;
}

.metric {
    text-align: center;
    flex: 1;
}

.metric-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.metric-value.tier-enterprise {
    color: #8e44ad;
}

.metric-value.tier-high {
    color: #27ae60;
}

.metric-value.tier-security {
    color: #e74c3c;
}

.metric-label {
    display: block;
    font-size: 11px;
    color: #7f8c8d;
    text-transform: uppercase;
    margin-top: 2px;
}

.provider-actions {
    display: flex;
    gap: 10px;
}

.select-provider-btn,
.test-provider-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid #4CAF50;
    background: #4CAF50;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.select-provider-btn:hover,
.test-provider-btn:hover {
    background: #45a049;
    border-color: #45a049;
}

.test-provider-btn {
    background: transparent;
    color: #4CAF50;
}

.test-provider-btn:hover {
    background: #4CAF50;
    color: white;
}

.existing-cdn-notice {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 12px 16px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #0066cc;
}

.existing-cdn-notice .dashicons {
    color: #0066cc;
}

/* Phase 4: Provider Configuration Card */
#provider-configuration-card {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* CDN Auto-Save Indicator */
.cdn-autosave-indicator {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 9999;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    display: none;
    align-items: center;
    gap: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.cdn-autosave-indicator.saving {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.cdn-autosave-indicator.saving .dashicons {
    animation: spin 1s linear infinite;
}

.cdn-autosave-indicator.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.cdn-autosave-indicator.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Ensure auto-save indicator appears above WordPress admin bar */
@media screen and (max-width: 782px) {
    .cdn-autosave-indicator {
        top: 46px;
        right: 10px;
        font-size: 12px;
        padding: 6px 12px;
    }
}
</style>

<div class="redco-module-tab" data-module="cdn-integration">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('CDN Integration', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-networking"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('CDN Integration', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Integrate with Content Delivery Networks to serve static assets from edge locations worldwide, reducing load times and server bandwidth.', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled && $cdn_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if (!empty($active_provider)): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-cloud"></span>
                                    <?php echo esc_html($providers[$active_provider]['name']); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($stats['cache_hit_ratio'] > 0): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-chart-line"></span>
                                    <?php _e('Optimized', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php elseif ($existing_cdn['detected']): ?>
                            <div class="status-badge warning">
                                <span class="dashicons dashicons-info"></span>
                                <?php printf(__('CDN Detected: %s', 'redco-optimizer'), ucfirst($existing_cdn['provider'])); ?>
                            </div>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled && $cdn_stats['enabled']): ?>
                            <button type="button" class="header-action-btn" id="test-cdn-connection-header">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Test Connection', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="purge-cdn-cache-header">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Purge Cache', 'redco-optimizer'); ?>
                            </button>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- CDN Metrics -->
                <?php if ($is_enabled && $cdn_enabled && !empty($active_provider)): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo !empty($active_provider) ? esc_html($providers[$active_provider]['name']) : 'N/A'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Provider', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo is_numeric($stats['cache_hit_ratio']) ? round($stats['cache_hit_ratio'], 1) : $stats['cache_hit_ratio']; ?>%
                            </div>
                            <div class="header-metric-label"><?php _e('Hit Rate', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo esc_html($provider_statuses[$active_provider] ?? 'unknown'); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Status', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="cdn-integration">
                    <?php wp_nonce_field('redco_cdn_integration_settings', 'redco_cdn_integration_nonce'); ?>

                    <!-- Enable CDN Integration -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('CDN Integration Settings', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Enable CDN integration to serve static assets from your selected CDN provider for faster global content delivery.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="setting-item enhanced">
                                <label class="setting-label checkbox-label">
                                    <input type="checkbox" name="cdn_enabled" value="1"
                                           <?php checked($cdn_enabled, 1); ?> class="enhanced-checkbox">
                                    <strong><?php _e('Enable CDN Integration', 'redco-optimizer'); ?></strong>
                                    <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                </label>
                                <div class="setting-description">
                                    <p><?php _e('Enable CDN integration to serve static assets from your selected CDN provider for faster global content delivery.', 'redco-optimizer'); ?></p>
                                    <div class="setting-benefits">
                                        <span class="benefit-item">🌍 Global delivery</span>
                                        <span class="benefit-item">⚡ Faster loading</span>
                                        <span class="benefit-item">📉 Reduced bandwidth</span>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="active_provider" value="<?php echo esc_attr($active_provider); ?>" id="selected_provider_input">
                        </div>
                    </div>

                    <!-- Phase 4: CDN Provider Selection -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-cloud"></span>
                                <?php _e('CDN Provider Selection', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Choose from enterprise-grade CDN providers to serve your content from edge locations worldwide. Each provider offers unique features and performance benefits.', 'redco-optimizer'); ?>
                                </p>

                                <?php if ($existing_cdn['detected']): ?>
                                <div class="existing-cdn-notice">
                                    <span class="dashicons dashicons-info"></span>
                                    <strong><?php _e('CDN Detected:', 'redco-optimizer'); ?></strong>
                                    <?php printf(__('We detected you\'re already using %s. You can configure it below for enhanced integration.', 'redco-optimizer'), ucfirst($existing_cdn['provider'])); ?>
                                </div>
                                <?php endif; ?>

                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="cdn-impact"><?php _e('High Performance', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <!-- CDN Provider Grid -->
                            <div class="cdn-provider-grid">
                                <?php foreach ($providers as $provider_name => $provider_data): ?>
                                <div class="cdn-provider-card <?php echo $active_provider === $provider_name ? 'active' : ''; ?> <?php echo $provider_name === $recommended_provider ? 'recommended' : ''; ?>" data-provider="<?php echo esc_attr($provider_name); ?>">
                                    <div class="provider-header">
                                        <div class="provider-logo">
                                            <span class="provider-icon dashicons dashicons-cloud"></span>
                                        </div>
                                        <div class="provider-info">
                                            <h4><?php echo esc_html($provider_data['name']); ?></h4>
                                            <?php if ($provider_name === $recommended_provider): ?>
                                                <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                            <?php endif; ?>
                                            <div class="provider-status status-<?php echo esc_attr($provider_statuses[$provider_name]); ?>">
                                                <?php echo esc_html(ucfirst(str_replace('_', ' ', $provider_statuses[$provider_name]))); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="provider-features">
                                        <ul>
                                            <?php foreach (array_slice($provider_data['features'], 0, 3) as $feature): ?>
                                            <li><span class="dashicons dashicons-yes"></span> <?php echo esc_html($feature); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>

                                    <div class="provider-metrics">
                                        <div class="metric">
                                            <span class="metric-value"><?php echo esc_html($provider_data['edge_locations']); ?></span>
                                            <span class="metric-label"><?php _e('Edge Locations', 'redco-optimizer'); ?></span>
                                        </div>
                                        <div class="metric">
                                            <span class="metric-value tier-<?php echo esc_attr($provider_data['performance_tier']); ?>">
                                                <?php echo esc_html(ucfirst($provider_data['performance_tier'])); ?>
                                            </span>
                                            <span class="metric-label"><?php _e('Performance', 'redco-optimizer'); ?></span>
                                        </div>
                                    </div>

                                    <div class="provider-actions">
                                        <button type="button" class="select-provider-btn" data-provider="<?php echo esc_attr($provider_name); ?>">
                                            <?php echo $active_provider === $provider_name ? __('Configure', 'redco-optimizer') : __('Select', 'redco-optimizer'); ?>
                                        </button>
                                        <?php if ($provider_statuses[$provider_name] === 'configured'): ?>
                                        <button type="button" class="test-provider-btn" data-provider="<?php echo esc_attr($provider_name); ?>">
                                            <?php _e('Test', 'redco-optimizer'); ?>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Phase 4: Provider Configuration Card -->
                    <div class="redco-card" id="provider-configuration-card" style="display: none;">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <span id="provider-config-title"><?php _e('Provider Configuration', 'redco-optimizer'); ?></span>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div id="provider-config-content">
                                <!-- Dynamic provider configuration will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- File Types Configuration -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-media-document"></span>
                                <?php _e('File Types Configuration', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Configure which file types should be served through your CDN for optimal performance.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="setting-item enhanced">
                                    <label class="setting-label">
                                        <strong><?php _e('File Types to Serve via CDN', 'redco-optimizer'); ?></strong>
                                        <span class="help-tooltip" title="<?php _e('Select which file types should be served through your CDN', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                    <div class="setting-control">
                                        <div class="checkbox-list">
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_images]" value="1"
                                                       <?php checked(isset($current_settings['enable_images']) ? $current_settings['enable_images'] : 1, 1); ?>
                                                       id="enable_images" class="enhanced-checkbox">
                                                <label for="enable_images"><?php _e('Images (JPG, PNG, GIF, WebP, SVG)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_css]" value="1"
                                                       <?php checked(isset($current_settings['enable_css']) ? $current_settings['enable_css'] : 1, 1); ?>
                                                       id="enable_css" class="enhanced-checkbox">
                                                <label for="enable_css"><?php _e('CSS Files', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_js]" value="1"
                                                       <?php checked(isset($current_settings['enable_js']) ? $current_settings['enable_js'] : 1, 1); ?>
                                                       id="enable_js" class="enhanced-checkbox">
                                                <label for="enable_js"><?php _e('JavaScript Files', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_fonts]" value="1"
                                                       <?php checked(isset($current_settings['enable_fonts']) ? $current_settings['enable_fonts'] : 1, 1); ?>
                                                       id="enable_fonts" class="enhanced-checkbox">
                                                <label for="enable_fonts"><?php _e('Font Files (WOFF, WOFF2, TTF)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_media]" value="1"
                                                       <?php checked(isset($current_settings['enable_media']) ? $current_settings['enable_media'] : 0, 1); ?>
                                                       id="enable_media" class="enhanced-checkbox">
                                                <label for="enable_media"><?php _e('Media Files (MP4, MP3, etc.)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_documents]" value="1"
                                                       <?php checked(isset($current_settings['enable_documents']) ? $current_settings['enable_documents'] : 0, 1); ?>
                                                       id="enable_documents" class="enhanced-checkbox">
                                                <label for="enable_documents"><?php _e('Documents (PDF, ZIP, etc.)', 'redco-optimizer'); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                <div class="setting-description">
                                    <p><?php _e('Select which file types should be served through your CDN. Images and CSS/JS files typically provide the best performance improvements.', 'redco-optimizer'); ?></p>
                                    <div class="setting-note">
                                        <span class="dashicons dashicons-info"></span>
                                        <?php _e('Tip: Start with images and CSS/JS files for immediate performance benefits.', 'redco-optimizer'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Advanced CDN Options', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Advanced configuration options for fine-tuning your CDN integration behavior.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="cdn_settings[skip_logged_in]" value="1"
                                               <?php checked(isset($current_settings['skip_logged_in']) ? $current_settings['skip_logged_in'] : 1, 1); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Skip CDN for Logged-in Users', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Disable CDN for logged-in users to avoid caching issues with dynamic content and admin functionality.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">🔒 Prevents admin issues</span>
                                            <span class="benefit-item">🎯 Dynamic content support</span>
                                            <span class="benefit-item">✅ Better compatibility</span>
                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- CDN Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('CDN Performance', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-requests">
                                <span class="stat-value"><?php echo number_format($cdn_stats['total_requests']); ?></span>
                                <span class="stat-label"><?php _e('Total Requests', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-hits">
                                <span class="stat-value"><?php echo number_format($cdn_stats['cache_hits']); ?></span>
                                <span class="stat-label"><?php _e('Cache Hits', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-ratio">
                                <span class="stat-value"><?php echo $cdn_stats['total_requests'] > 0 ? round(($cdn_stats['cache_hits'] / $cdn_stats['total_requests']) * 100, 1) : 0; ?>%</span>
                                <span class="stat-label"><?php _e('Hit Ratio', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-bandwidth">
                                <span class="stat-value"><?php echo size_format($cdn_stats['bandwidth_saved']); ?></span>
                                <span class="stat-label"><?php _e('Bandwidth Saved', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CDN Management Actions -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('CDN Management', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="sidebar-actions">
                            <div class="sidebar-action-item">
                                <button type="button" id="test-cdn-connection" class="sidebar-action-button">
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    <?php _e('Test Connection', 'redco-optimizer'); ?>
                                </button>
                                <p class="sidebar-action-description"><?php _e('Verify CDN configuration', 'redco-optimizer'); ?></p>
                            </div>

                            <div class="sidebar-action-item">
                                <button type="button" id="purge-cdn-cache" class="sidebar-action-button">
                                    <span class="dashicons dashicons-trash"></span>
                                    <?php _e('Purge Cache', 'redco-optimizer'); ?>
                                </button>
                                <p class="sidebar-action-description"><?php _e('Clear cached content', 'redco-optimizer'); ?></p>
                            </div>
                        </div>

                        <div id="cdn-test-result" class="sidebar-test-result" style="display:none;"></div>
                        <div id="cdn-purge-result" class="sidebar-test-result" style="display:none;"></div>
                    </div>
                </div>

                <!-- CDN Provider Info -->
                <?php if (!empty($active_provider)): ?>
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-cloud"></span>
                            <?php _e('Provider Info', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="provider-info">
                            <div class="provider-name">
                                <strong><?php echo esc_html($providers[$active_provider]['name']); ?></strong>
                            </div>
                            <div class="provider-status">
                                <?php if ($cdn_stats['enabled']): ?>
                                    <span class="status-badge enabled">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        <?php _e('Connected', 'redco-optimizer'); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge disabled">
                                        <span class="dashicons dashicons-warning"></span>
                                        <?php _e('Not Connected', 'redco-optimizer'); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Quick Tips -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-lightbulb"></span>
                            <?php _e('Optimization Tips', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="tips-list">
                            <div class="tip-item">
                                <span class="tip-icon">💡</span>
                                <span class="tip-text"><?php _e('Start with images and CSS/JS files for immediate performance benefits.', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="tip-item">
                                <span class="tip-icon">🌍</span>
                                <span class="tip-text"><?php _e('CDN provides the most benefit for users far from your server location.', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="tip-item">
                                <span class="tip-icon">📊</span>
                                <span class="tip-text"><?php _e('Monitor your CDN hit ratio - aim for 80% or higher for optimal performance.', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="tip-item">
                                <span class="tip-icon">🔄</span>
                                <span class="tip-text"><?php _e('Purge CDN cache after major site updates to ensure fresh content delivery.', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
        <div class="module-disabled-notice">
            <div class="redco-notice warning">
                <p><?php _e('This module is currently disabled. Enable it to access CDN integration settings.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Phase 4: CDN Provider Selection
    $('.select-provider-btn').on('click', function() {
        var provider = $(this).data('provider');
        var $card = $(this).closest('.cdn-provider-card');
        var providerName = $card.find('h4').text();

        console.log('Provider selected:', provider, providerName);

        // Update active provider
        $('.cdn-provider-card').removeClass('active');
        $card.addClass('active');

        // Update hidden input
        $('#selected_provider_input').val(provider);

        // Show provider configuration card
        $('#provider-configuration-card').show();
        $('#provider-config-title').text(providerName + ' Configuration');

        // Load basic provider configuration form
        loadProviderConfiguration(provider, providerName);

        // Trigger auto-save
        triggerCdnAutoSave();
    });

    // Load provider configuration dynamically
    function loadProviderConfiguration(provider, providerName) {
        var $content = $('#provider-config-content');

        // Create basic configuration form based on provider
        var configHtml = '<div class="provider-config-form">';
        configHtml += '<div class="settings-intro">';
        configHtml += '<p class="description">Configure your ' + providerName + ' CDN settings below.</p>';
        configHtml += '</div>';

        // Add provider-specific fields
        switch(provider) {
            case 'cloudflare':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Zone ID</strong> <span class="help-tooltip" title="Your Cloudflare Zone ID">?</span></label>';
                configHtml += '<div class="setting-control"><input type="text" name="cdn_settings[cloudflare_zone_id]" class="enhanced-input" placeholder="Your Zone ID"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Token</strong> <span class="help-tooltip" title="Your Cloudflare API Token">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[cloudflare_api_token]" class="enhanced-input" placeholder="Your API Token"></div>';
                configHtml += '</div>';
                break;
            case 'bunnycdn':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Key</strong> <span class="help-tooltip" title="Your BunnyCDN API Key">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[bunnycdn_api_key]" class="enhanced-input" placeholder="Your API Key"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Pull Zone URL</strong> <span class="help-tooltip" title="Your BunnyCDN Pull Zone URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[bunnycdn_pull_zone_url]" class="enhanced-input" placeholder="https://your-zone.b-cdn.net"></div>';
                configHtml += '</div>';
                break;
            case 'keycdn':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Key</strong> <span class="help-tooltip" title="Your KeyCDN API Key">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[keycdn_api_key]" class="enhanced-input" placeholder="Your API Key"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Zone URL</strong> <span class="help-tooltip" title="Your KeyCDN Zone URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[keycdn_zone_url]" class="enhanced-input" placeholder="https://your-zone.kxcdn.com"></div>';
                configHtml += '</div>';
                break;
            case 'amazon_cloudfront':
                configHtml += '<div class="setting-note"><span class="dashicons dashicons-info"></span> Note: Full CloudFront integration requires AWS SDK. Basic configuration is available.</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Distribution ID</strong> <span class="help-tooltip" title="Your CloudFront Distribution ID">?</span></label>';
                configHtml += '<div class="setting-control"><input type="text" name="cdn_settings[cloudfront_distribution_id]" class="enhanced-input" placeholder="E1234567890123"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Distribution URL</strong> <span class="help-tooltip" title="Your CloudFront Distribution URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[cloudfront_distribution_url]" class="enhanced-input" placeholder="https://d1234567890123.cloudfront.net"></div>';
                configHtml += '</div>';
                break;
            case 'sucuri':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Key</strong> <span class="help-tooltip" title="Your Sucuri API Key">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[sucuri_api_key]" class="enhanced-input" placeholder="Your API Key"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>CDN URL</strong> <span class="help-tooltip" title="Your Sucuri CDN URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[sucuri_cdn_url]" class="enhanced-input" placeholder="https://your-domain.sucuri.net"></div>';
                configHtml += '</div>';
                break;
            case 'fastly':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>API Token</strong> <span class="help-tooltip" title="Your Fastly API Token">?</span></label>';
                configHtml += '<div class="setting-control"><input type="password" name="cdn_settings[fastly_api_token]" class="enhanced-input" placeholder="Your API Token"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>Service ID</strong> <span class="help-tooltip" title="Your Fastly Service ID">?</span></label>';
                configHtml += '<div class="setting-control"><input type="text" name="cdn_settings[fastly_service_id]" class="enhanced-input" placeholder="1234567890abcdef"></div>';
                configHtml += '</div>';
                break;
            case 'custom':
                configHtml += '<div class="setting-item enhanced">';
                configHtml += '<label class="setting-label"><strong>CDN URL</strong> <span class="help-tooltip" title="Your custom CDN URL">?</span></label>';
                configHtml += '<div class="setting-control"><input type="url" name="cdn_settings[custom_cdn_url]" class="enhanced-input" placeholder="https://cdn.your-domain.com"></div>';
                configHtml += '</div>';
                configHtml += '<div class="setting-note"><span class="dashicons dashicons-info"></span> Note: Cache purging and advanced features may not be available with custom CDN configurations.</div>';
                break;
            default:
                configHtml += '<div class="setting-note"><span class="dashicons dashicons-info"></span> Configuration options will be available once you select a provider.</div>';
        }

        configHtml += '</div>';

        $content.html(configHtml);

        // Bind auto-save to new fields
        $content.find('input').on('input change', function() {
            triggerCdnAutoSave();
        });
    }

    // Test CDN connection (both header and sidebar buttons)
    $('#test-cdn-connection, #test-cdn-connection-header').on('click', function() {
        var $button = $(this);
        var $result = $('#cdn-test-result');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Testing...');
        $result.hide().removeClass('success error');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_test_cdn_connection',
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $result.addClass('success')
                           .html('<strong>Success:</strong> ' + response.data.message).show();
                } else {
                    $result.addClass('error')
                           .html('<strong>Error:</strong> ' + response.data.message).show();
                }
            },
            error: function() {
                $result.addClass('error')
                       .html('<strong>Error:</strong> Failed to test CDN connection').show();
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    });

    // Purge CDN cache (both header and sidebar buttons)
    $('#purge-cdn-cache, #purge-cdn-cache-header').on('click', function() {
        var $button = $(this);
        var $result = $('#cdn-purge-result');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Purging...');
        $result.hide().removeClass('success error');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_purge_cdn_cache',
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $result.addClass('success')
                           .html('<strong>Success:</strong> ' + response.data.message).show();
                } else {
                    $result.addClass('error')
                           .html('<strong>Error:</strong> ' + response.data.message).show();
                }
            },
            error: function() {
                $result.addClass('error')
                       .html('<strong>Error:</strong> Failed to purge CDN cache').show();
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    });

    // CDN Auto-Save Functionality
    var cdnAutoSaveTimer = null;
    var cdnAutoSaveInProgress = false;
    var cdnLastSavedData = null;

    // Auto-save status indicator
    function showCdnAutoSaveStatus(status, message) {
        var $indicator = $('#cdn-autosave-indicator');
        if (!$indicator.length) {
            $indicator = $('<div id="cdn-autosave-indicator" class="cdn-autosave-indicator"></div>');
            $('.redco-module-form[data-module="cdn-integration"]').prepend($indicator);
        }

        $indicator.removeClass('saving success error').addClass(status);
        $indicator.html('<span class="dashicons dashicons-' +
            (status === 'saving' ? 'update' : status === 'success' ? 'yes-alt' : 'warning') +
            '"></span>' + message);

        $indicator.show();

        if (status === 'success' || status === 'error') {
            setTimeout(function() {
                $indicator.fadeOut();
            }, status === 'success' ? 2000 : 5000);
        }
    }

    // Debounced auto-save function
    function triggerCdnAutoSave() {
        if (cdnAutoSaveInProgress) {
            return;
        }

        clearTimeout(cdnAutoSaveTimer);
        cdnAutoSaveTimer = setTimeout(function() {
            performCdnAutoSave();
        }, 2500); // 2.5 second delay
    }

    // Perform auto-save
    function performCdnAutoSave() {
        if (cdnAutoSaveInProgress) {
            return;
        }

        var $form = $('.redco-module-form[data-module="cdn-integration"]');
        var formData = $form.serializeArray();
        var settings = {};

        // Convert form data to object - Phase 4 format
        $.each(formData, function(i, field) {
            if (field.name.startsWith('cdn_settings[') && field.name.endsWith(']')) {
                var fieldName = field.name.replace('cdn_settings[', '').replace(']', '');
                settings[fieldName] = field.value;
            } else if (field.name === 'cdn_enabled' || field.name === 'active_provider') {
                settings[field.name] = field.value;
            }
        });

        // Handle checkboxes explicitly - Phase 4 format
        $form.find('input[type="checkbox"]').each(function() {
            var $checkbox = $(this);
            var name = $checkbox.attr('name');
            if (name && name.startsWith('cdn_settings[')) {
                var fieldName = name.replace('cdn_settings[', '').replace(']', '');
                settings[fieldName] = $checkbox.is(':checked') ? 1 : 0;
            } else if (name === 'cdn_enabled') {
                settings[name] = $checkbox.is(':checked') ? 1 : 0;
            }
        });

        // Check if data has changed
        var currentDataString = JSON.stringify(settings);
        if (currentDataString === cdnLastSavedData) {
            return;
        }

        cdnAutoSaveInProgress = true;
        showCdnAutoSaveStatus('saving', 'Saving CDN settings...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_save_cdn_settings',
                settings: settings,
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    cdnLastSavedData = currentDataString;
                    showCdnAutoSaveStatus('success', 'CDN settings saved');
                } else {
                    showCdnAutoSaveStatus('error', response.data.message || 'Error saving settings');
                }
            },
            error: function(xhr, status, error) {
                var errorMessage = xhr.status === 500 ?
                    'Server error - Check error logs' :
                    'Network error: ' + error;
                showCdnAutoSaveStatus('error', errorMessage);
            },
            complete: function() {
                cdnAutoSaveInProgress = false;
            }
        });
    }

    // Bind auto-save to form changes
    $('.redco-module-form[data-module="cdn-integration"]').on('input change', 'input, select, textarea', function() {
        triggerCdnAutoSave();
    });

    // Special handling for provider selection to trigger auto-save
    $('.select-provider-btn').on('click', function() {
        setTimeout(function() {
            triggerCdnAutoSave();
        }, 100); // Small delay to allow configuration loading
    });

    // Initialize auto-save with current form state
    function initializeCdnAutoSave() {
        var $form = $('.redco-module-form[data-module="cdn-integration"]');
        if ($form.length) {
            var formData = $form.serializeArray();
            var settings = {};

            // Convert form data to object - Phase 4 format
            $.each(formData, function(i, field) {
                if (field.name.startsWith('cdn_settings[') && field.name.endsWith(']')) {
                    var fieldName = field.name.replace('cdn_settings[', '').replace(']', '');
                    settings[fieldName] = field.value;
                } else if (field.name === 'cdn_enabled' || field.name === 'active_provider') {
                    settings[field.name] = field.value;
                }
            });

            // Handle checkboxes explicitly - Phase 4 format
            $form.find('input[type="checkbox"]').each(function() {
                var $checkbox = $(this);
                var name = $checkbox.attr('name');
                if (name && name.startsWith('cdn_settings[')) {
                    var fieldName = name.replace('cdn_settings[', '').replace(']', '');
                    settings[fieldName] = $checkbox.is(':checked') ? 1 : 0;
                } else if (name === 'cdn_enabled') {
                    settings[name] = $checkbox.is(':checked') ? 1 : 0;
                }
            });

            // Store initial state
            cdnLastSavedData = JSON.stringify(settings);
        }
    }

    // Initialize auto-save when page loads
    initializeCdnAutoSave();

    // Performance impact indicator for Phase 4
    $('.select-provider-btn').on('click', function() {
        var $impact = $('#cdn-impact');
        $impact.removeClass('low medium').addClass('high').text('High Performance');
    });
});
</script>
